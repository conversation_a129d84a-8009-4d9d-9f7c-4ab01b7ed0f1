"""Workspace management for OLV Launcher."""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from .models.workspace import (
    WorkspaceConfig,
    WorkspaceState,
    WorkspacePluginState,
    WorkspacePluginInstance,
)
from .plugin_manager import UnifiedPluginManager
from .exceptions import WorkspaceError

logger = logging.getLogger(__name__)


class WorkspaceManager:
    """Manages workspace configurations and state persistence."""

    def __init__(self, config_file: Path, plugin_manager: UnifiedPluginManager):
        """
        Initialize the workspace manager.

        Args:
            config_file: Path to the workspace configuration file
            plugin_manager: Plugin manager instance
        """
        self.config_file = config_file
        self.plugin_manager = plugin_manager
        self._config: Optional[WorkspaceConfig] = None

        # Ensure config directory exists
        self.config_file.parent.mkdir(parents=True, exist_ok=True)

        # Load existing config
        self._load_config()

        # Clear current workspace on startup since system state is reset
        if self._config.current_workspace:
            logger.info(f"Clearing current workspace '{self._config.current_workspace}' on startup")
            self._config.current_workspace = None
            self._save_config()

        # Create a default workspace if none exist
        if not self._config.workspaces:
            self._create_default_workspace()

    def _load_config(self) -> None:
        """Load workspace configuration from file."""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                self._config = WorkspaceConfig(**config_data)
                logger.info(f"Loaded workspace config from {self.config_file}")
            else:
                self._config = WorkspaceConfig()
                logger.info("Created new workspace config")
        except Exception as e:
            logger.error(f"Failed to load workspace config: {e}")
            self._config = WorkspaceConfig()

    def _save_config(self) -> None:
        """Save workspace configuration to file."""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(
                    self._config.model_dump(mode='json'),
                    f,
                    indent=2,
                    ensure_ascii=False,
                    default=str
                )
            logger.info(f"Saved workspace config to {self.config_file}")
        except Exception as e:
            logger.error(f"Failed to save workspace config: {e}")
            raise WorkspaceError(f"Failed to save workspace config: {e}")

    def get_config(self) -> WorkspaceConfig:
        """Get the current workspace configuration."""
        return self._config

    def list_workspaces(self) -> List[WorkspaceState]:
        """List all available workspaces."""
        return list(self._config.workspaces.values())

    async def save_current_state(self, name: Optional[str] = None, description: Optional[str] = None, save_to_current: bool = False) -> Tuple[int, int]:
        """
        Save the current system state as a workspace.

        Args:
            name: Workspace name (if None and save_to_current is True, uses current workspace)
            description: Optional workspace description
            save_to_current: Whether to save to current workspace

        Returns:
            Tuple of (saved_plugins_count, saved_instances_count)

        Raises:
            WorkspaceError: If saving fails
        """
        # Determine the workspace name to use
        if save_to_current and self._config.current_workspace:
            workspace_name = self._config.current_workspace
        elif name:
            workspace_name = name
        else:
            # Create a default workspace name if none provided
            workspace_name = "Untitled Workspace"
            counter = 1
            while workspace_name in self._config.workspaces:
                workspace_name = f"Untitled Workspace {counter}"
                counter += 1

        try:
            # Get all plugins
            all_plugins = self.plugin_manager.get_all_plugins()

            workspace_plugins = []
            total_instances = 0

            for plugin_data in all_plugins:
                plugin_name = plugin_data['name']
                is_running = self.plugin_manager.is_plugin_running(plugin_name)

                # Get instances if plugin is running
                instances = []
                if is_running:
                    try:
                        instances_data = await self.plugin_manager.list_plugin_instances(plugin_name)
                        instance_dict = instances_data.get('instances', {}).get('instances', {})

                        for instance_id, instance_info in instance_dict.items():
                            # We don't have the original config, so we'll store a placeholder
                            # In a real implementation, you might want to store configs separately
                            instances.append(WorkspacePluginInstance(
                                instance_id=instance_id,
                                config={}  # Config would need to be retrieved from plugin
                            ))
                            total_instances += 1
                    except Exception as e:
                        logger.warning(f"Failed to get instances for plugin {plugin_name}: {e}")

                workspace_plugins.append(WorkspacePluginState(
                    name=plugin_name,
                    should_be_running=is_running,
                    instances=instances
                ))

            # Create workspace state
            workspace = WorkspaceState(
                name=workspace_name,
                created_at=datetime.now(),
                description=description,
                plugins=workspace_plugins
            )

            # Save to config
            self._config.workspaces[workspace_name] = workspace

            # Only set as current workspace if explicitly saving to current or restoring
            if save_to_current or not self._config.current_workspace:
                self._config.current_workspace = workspace_name

            self._save_config()

            logger.info(f"Saved workspace '{workspace_name}' with {len(workspace_plugins)} plugins and {total_instances} instances")
            return len(workspace_plugins), total_instances

        except Exception as e:
            logger.error(f"Failed to save workspace '{workspace_name}': {e}")
            raise WorkspaceError(f"Failed to save workspace: {e}")

    async def restore_workspace(self, name: str) -> Tuple[int, int, List[str]]:
        """
        Restore a workspace state.

        Args:
            name: Workspace name to restore

        Returns:
            Tuple of (restored_plugins_count, restored_instances_count, errors)

        Raises:
            WorkspaceError: If workspace doesn't exist or restoration fails
        """
        if name not in self._config.workspaces:
            raise WorkspaceError(f"Workspace '{name}' not found")

        workspace = self._config.workspaces[name]
        errors = []
        restored_plugins = 0
        restored_instances = 0

        try:
            # First, stop all currently running plugins
            all_plugins = self.plugin_manager.get_all_plugins()
            for plugin_data in all_plugins:
                plugin_name = plugin_data['name']
                if self.plugin_manager.is_plugin_running(plugin_name):
                    try:
                        await self.plugin_manager.stop_plugin_service(plugin_name)
                        logger.info(f"Stopped plugin {plugin_name}")
                    except Exception as e:
                        logger.warning(f"Failed to stop plugin {plugin_name}: {e}")
                        errors.append(f"Failed to stop plugin {plugin_name}: {e}")

            # Restore workspace state
            for plugin_state in workspace.plugins:
                plugin_name = plugin_state.name

                # Check if plugin exists
                if plugin_name not in self.plugin_manager.plugins:
                    errors.append(f"Plugin '{plugin_name}' not found")
                    continue

                # Start plugin if it should be running
                if plugin_state.should_be_running:
                    try:
                        await self.plugin_manager.start_plugin_service(plugin_name)
                        restored_plugins += 1
                        logger.info(f"Started plugin {plugin_name}")

                        # Restore instances
                        for instance in plugin_state.instances:
                            try:
                                # Note: We can't restore the exact config since we didn't save it
                                # This is a limitation of the current implementation
                                # In practice, you'd want to save and restore the actual configs
                                await self.plugin_manager.create_plugin_instance(
                                    plugin_name,
                                    instance.config
                                )
                                restored_instances += 1
                                logger.info(f"Restored instance {instance.instance_id} for plugin {plugin_name}")
                            except Exception as e:
                                logger.warning(f"Failed to restore instance {instance.instance_id}: {e}")
                                errors.append(f"Failed to restore instance {instance.instance_id}: {e}")

                    except Exception as e:
                        logger.error(f"Failed to start plugin {plugin_name}: {e}")
                        errors.append(f"Failed to start plugin {plugin_name}: {e}")

            # Update current workspace
            self._config.current_workspace = name
            self._save_config()

            logger.info(f"Restored workspace '{name}' with {restored_plugins} plugins and {restored_instances} instances")
            return restored_plugins, restored_instances, errors

        except Exception as e:
            logger.error(f"Failed to restore workspace '{name}': {e}")
            raise WorkspaceError(f"Failed to restore workspace: {e}")

    def delete_workspace(self, name: str) -> None:
        """
        Delete a workspace.

        Args:
            name: Workspace name to delete

        Raises:
            WorkspaceError: If workspace doesn't exist
        """
        if name not in self._config.workspaces:
            raise WorkspaceError(f"Workspace '{name}' not found")

        del self._config.workspaces[name]

        # Clear current workspace if it was the deleted one
        if self._config.current_workspace == name:
            self._config.current_workspace = None

        self._save_config()
        logger.info(f"Deleted workspace '{name}'")

    def get_workspace(self, name: str) -> WorkspaceState:
        """
        Get a specific workspace.

        Args:
            name: Workspace name

        Returns:
            Workspace state

        Raises:
            WorkspaceError: If workspace doesn't exist
        """
        if name not in self._config.workspaces:
            raise WorkspaceError(f"Workspace '{name}' not found")

        return self._config.workspaces[name]

    async def detect_current_workspace(self) -> Optional[str]:
        """
        Detect if the current system state matches any saved workspace.

        Returns:
            Workspace name if a match is found, None otherwise
        """
        try:
            # Get current system state
            all_plugins = self.plugin_manager.get_all_plugins()
            current_running_plugins = set()

            for plugin_data in all_plugins:
                plugin_name = plugin_data['name']
                if self.plugin_manager.is_plugin_running(plugin_name):
                    current_running_plugins.add(plugin_name)

            # Check each workspace to see if it matches current state
            for workspace_name, workspace in self._config.workspaces.items():
                workspace_running_plugins = set()

                for plugin_state in workspace.plugins:
                    if plugin_state.should_be_running:
                        workspace_running_plugins.add(plugin_state.name)

                # If the sets match, this workspace matches current state
                if current_running_plugins == workspace_running_plugins:
                    return workspace_name

            return None

        except Exception as e:
            logger.warning(f"Failed to detect current workspace: {e}")
            return None
