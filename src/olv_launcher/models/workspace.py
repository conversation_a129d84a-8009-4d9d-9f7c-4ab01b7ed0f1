"""Workspace management models for OLV Launcher."""

from datetime import datetime
from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field


class WorkspacePluginInstance(BaseModel):
    """Model for a plugin instance in a workspace."""

    instance_id: str = Field(..., description="Instance ID")
    config: Dict[str, Any] = Field(..., description="Instance configuration")


class WorkspacePluginState(BaseModel):
    """Model for a plugin's state in a workspace."""

    name: str = Field(..., description="Plugin name")
    should_be_running: bool = Field(..., description="Whether the plugin should be running")
    instances: List[WorkspacePluginInstance] = Field(
        default_factory=list, description="Plugin instances"
    )


class WorkspaceState(BaseModel):
    """Model for a complete workspace state."""

    name: str = Field(..., description="Workspace name")
    created_at: datetime = Field(default_factory=datetime.now, description="Creation timestamp")
    description: Optional[str] = Field(default=None, description="Workspace description")
    plugins: List[WorkspacePluginState] = Field(
        default_factory=list, description="Plugin states"
    )


class WorkspaceConfig(BaseModel):
    """Model for the complete workspace configuration."""

    workspaces: Dict[str, WorkspaceState] = Field(
        default_factory=dict, description="All saved workspaces"
    )
    current_workspace: Optional[str] = Field(
        default=None, description="Currently active workspace"
    )


# API Request/Response models
class WorkspaceSaveRequest(BaseModel):
    """Request model for saving a workspace."""

    name: Optional[str] = Field(default=None, description="Workspace name (if None, saves to current workspace)")
    description: Optional[str] = Field(default=None, description="Workspace description")
    save_to_current: bool = Field(default=False, description="Whether to save to current workspace")


class WorkspaceSaveResponse(BaseModel):
    """Response model for saving a workspace."""

    message: str = Field(..., description="Success message")
    workspace_name: str = Field(..., description="Saved workspace name")
    saved_plugins: int = Field(..., description="Number of plugins saved")
    saved_instances: int = Field(..., description="Number of instances saved")


class WorkspaceRestoreResponse(BaseModel):
    """Response model for restoring a workspace."""

    message: str = Field(..., description="Success message")
    workspace_name: str = Field(..., description="Restored workspace name")
    restored_plugins: int = Field(..., description="Number of plugins restored")
    restored_instances: int = Field(..., description="Number of instances restored")
    errors: Optional[List[str]] = Field(default=None, description="Any errors that occurred")


class WorkspaceListResponse(BaseModel):
    """Response model for listing workspaces."""

    workspaces: List[WorkspaceState] = Field(..., description="Available workspaces")
    current_workspace: Optional[str] = Field(default=None, description="Current workspace")


class WorkspaceDeleteResponse(BaseModel):
    """Response model for deleting a workspace."""

    message: str = Field(..., description="Success message")
    workspace_name: str = Field(..., description="Deleted workspace name")
