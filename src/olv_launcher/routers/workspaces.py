"""Workspace management API routes."""

import logging
from typing import Annotated

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, Path, status

from ..dependencies import get_workspace_manager
from ..exceptions import WorkspaceError
from ..models.workspace import (
    WorkspaceConfig,
    WorkspaceDeleteResponse,
    WorkspaceListResponse,
    WorkspaceRestoreResponse,
    WorkspaceSaveRequest,
    WorkspaceSaveResponse,
)
from ..workspace_manager import WorkspaceManager

logger = logging.getLogger(__name__)

# Dependency type annotation
WorkspaceManagerDep = Annotated[WorkspaceManager, Depends(get_workspace_manager)]

router = APIRouter()


@router.post(
    "/save",
    response_model=WorkspaceSaveResponse,
    summary="Save current workspace state",
    description="Save the current system state (running plugins and instances) as a workspace",
)
async def save_workspace(
    request: WorkspaceSaveRequest,
    workspace_manager: WorkspaceManagerDep,
) -> WorkspaceSaveResponse:
    """Save the current system state as a workspace."""
    try:
        saved_plugins, saved_instances = await workspace_manager.save_current_state(
            request.name, request.description, request.save_to_current
        )

        # Determine the actual workspace name that was used
        if request.save_to_current and workspace_manager.get_config().current_workspace:
            actual_name = workspace_manager.get_config().current_workspace
        elif request.name:
            actual_name = request.name
        else:
            # Find the most recently created workspace (this is a bit hacky, but works)
            workspaces = workspace_manager.list_workspaces()
            actual_name = max(workspaces, key=lambda w: w.created_at).name if workspaces else "Untitled Workspace"

        return WorkspaceSaveResponse(
            message=f"Workspace '{actual_name}' saved successfully",
            workspace_name=actual_name,
            saved_plugins=saved_plugins,
            saved_instances=saved_instances,
        )
    except WorkspaceError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"Failed to save workspace '{request.name}': {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to save workspace: {e}",
        )


@router.post(
    "/{workspace_name}/restore",
    response_model=WorkspaceRestoreResponse,
    summary="Restore workspace state",
    description="Restore a previously saved workspace state",
)
async def restore_workspace(
    workspace_name: str = Path(..., description="Name of the workspace to restore"),
    workspace_manager: WorkspaceManagerDep = None,
) -> WorkspaceRestoreResponse:
    """Restore a workspace state."""
    try:
        restored_plugins, restored_instances, errors = await workspace_manager.restore_workspace(
            workspace_name
        )

        return WorkspaceRestoreResponse(
            message=f"Workspace '{workspace_name}' restored successfully",
            workspace_name=workspace_name,
            restored_plugins=restored_plugins,
            restored_instances=restored_instances,
            errors=errors if errors else None,
        )
    except WorkspaceError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"Failed to restore workspace '{workspace_name}': {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to restore workspace: {e}",
        )


@router.get(
    "/",
    response_model=WorkspaceListResponse,
    summary="List all workspaces",
    description="Get a list of all saved workspaces",
)
async def list_workspaces(
    workspace_manager: WorkspaceManagerDep,
) -> WorkspaceListResponse:
    """List all available workspaces."""
    try:
        workspaces = workspace_manager.list_workspaces()

        # Detect current workspace based on actual system state
        current_workspace = await workspace_manager.detect_current_workspace()

        return WorkspaceListResponse(
            workspaces=workspaces,
            current_workspace=current_workspace,
        )
    except Exception as e:
        logger.error(f"Failed to list workspaces: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list workspaces: {e}",
        )


@router.delete(
    "/{workspace_name}",
    response_model=WorkspaceDeleteResponse,
    summary="Delete workspace",
    description="Delete a saved workspace",
)
async def delete_workspace(
    workspace_name: str = Path(..., description="Name of the workspace to delete"),
    workspace_manager: WorkspaceManagerDep = None,
) -> WorkspaceDeleteResponse:
    """Delete a workspace."""
    try:
        workspace_manager.delete_workspace(workspace_name)

        return WorkspaceDeleteResponse(
            message=f"Workspace '{workspace_name}' deleted successfully",
            workspace_name=workspace_name,
        )
    except WorkspaceError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"Failed to delete workspace '{workspace_name}': {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete workspace: {e}",
        )


@router.get(
    "/config",
    response_model=WorkspaceConfig,
    summary="Get workspace configuration",
    description="Get the complete workspace configuration including all workspaces",
)
async def get_workspace_config(
    workspace_manager: WorkspaceManagerDep,
) -> WorkspaceConfig:
    """Get the workspace configuration."""
    try:
        return workspace_manager.get_config()
    except Exception as e:
        logger.error(f"Failed to get workspace config: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get workspace config: {e}",
        )
