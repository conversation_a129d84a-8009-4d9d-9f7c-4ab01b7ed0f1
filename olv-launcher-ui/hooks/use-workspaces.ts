import { useState, useCallback } from 'react';
import useS<PERSON> from 'swr';
import { 
  apiClient, 
  WorkspaceListResponse, 
  WorkspaceConfig,
  WorkspaceSaveRequest,
  WorkspaceSaveResponse,
  WorkspaceRestoreResponse,
  WorkspaceDeleteResponse
} from '@/lib/api';
import { toast } from 'sonner';

export const useWorkspaces = () => {
  return useSWR<WorkspaceListResponse>('workspaces', apiClient.listWorkspaces, {
    refreshInterval: 0, // Don't auto-refresh workspaces
    onError: (error) => {
      console.error('Failed to fetch workspaces:', error);
    },
  });
};

export const useWorkspaceConfig = () => {
  return useSWR<WorkspaceConfig>('workspaces/config', apiClient.getWorkspaceConfig, {
    refreshInterval: 0, // Don't auto-refresh config
    onError: (error) => {
      console.error('Failed to fetch workspace config:', error);
    },
  });
};

export const useWorkspaceActions = () => {
  const [loading, setLoading] = useState<Record<string, boolean>>({});

  const saveWorkspace = useCallback(async (request: WorkspaceSaveRequest): Promise<WorkspaceSaveResponse> => {
    setLoading(prev => ({ ...prev, save: true }));
    try {
      const result = await apiClient.saveWorkspace(request);
      toast.success(
        `Workspace "${request.name}" saved successfully! ` +
        `Saved ${result.saved_plugins} plugins with ${result.saved_instances} instances.`
      );
      return result;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to save workspace: ${errorMessage}`);
      throw error;
    } finally {
      setLoading(prev => ({ ...prev, save: false }));
    }
  }, []);

  const restoreWorkspace = useCallback(async (workspaceName: string): Promise<WorkspaceRestoreResponse> => {
    setLoading(prev => ({ ...prev, [workspaceName]: true }));
    try {
      const result = await apiClient.restoreWorkspace(workspaceName);
      
      if (result.errors && result.errors.length > 0) {
        toast.warning(
          `Workspace "${workspaceName}" restored with warnings. ` +
          `Restored ${result.restored_plugins} plugins with ${result.restored_instances} instances. ` +
          `${result.errors.length} errors occurred.`
        );
      } else {
        toast.success(
          `Workspace "${workspaceName}" restored successfully! ` +
          `Restored ${result.restored_plugins} plugins with ${result.restored_instances} instances.`
        );
      }
      
      return result;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to restore workspace "${workspaceName}": ${errorMessage}`);
      throw error;
    } finally {
      setLoading(prev => ({ ...prev, [workspaceName]: false }));
    }
  }, []);

  const deleteWorkspace = useCallback(async (workspaceName: string): Promise<WorkspaceDeleteResponse> => {
    setLoading(prev => ({ ...prev, [`delete_${workspaceName}`]: true }));
    try {
      const result = await apiClient.deleteWorkspace(workspaceName);
      toast.success(`Workspace "${workspaceName}" deleted successfully`);
      return result;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to delete workspace "${workspaceName}": ${errorMessage}`);
      throw error;
    } finally {
      setLoading(prev => ({ ...prev, [`delete_${workspaceName}`]: false }));
    }
  }, []);

  return {
    saveWorkspace,
    restoreWorkspace,
    deleteWorkspace,
    loading,
  };
};
