/* eslint-disable react-hooks/exhaustive-deps */
import { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, AlertCircle, Settings, RefreshCw, Plus, Trash2, Database, Copy, Eye, CheckCircle, Clock } from 'lucide-react';
import { apiClient, PluginSchemasResponse, PluginInstanceListResponse, PluginInstanceInfo, PluginStatus } from '@/lib/api';
import { toast } from 'sonner';

// Import RJSF with shadcn theme
import { RJSFSchema, UiSchema, RJSFValidationError } from '@rjsf/utils';
import validator from '@rjsf/validator-ajv8';
import { generateForm } from '@rjsf/shadcn';

interface PluginConfigDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  pluginName: string;
  pluginStatus?: PluginStatus;
}

// Form data type
type FormData = Record<string, unknown>;

// Import RJSF types
import { IChangeEvent } from '@rjsf/core';

export function PluginConfigDialog({ open, onOpenChange, pluginName, pluginStatus }: PluginConfigDialogProps) {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [instancesLoading, setInstancesLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [schemas, setSchemas] = useState<PluginSchemasResponse | null>(null);
  const [instances, setInstances] = useState<PluginInstanceListResponse | null>(null);
  
  // Form data state - for plugin configuration
  const [pluginFormData, setPluginFormData] = useState<FormData>({});

  useEffect(() => {
    if (open && pluginName) {
      // Check if plugin is running before attempting to load schemas
      if (pluginStatus && pluginStatus !== PluginStatus.RUNNING) {
        setError(`Plugin ${pluginName} is not running (status: ${pluginStatus})`);
        setLoading(false);
        return;
      }
      
      loadSchemas();
      loadInstances();
    } else {
      // Reset state when dialog closes
      setError(null);
      setInstances(null);
    }
  }, [open, pluginName, pluginStatus]);

  const loadSchemas = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await apiClient.getPluginSchemas(pluginName);
      setSchemas(response);
      
      // Initialize form data with default values if available
      if (response.schemas.plugin_json_schema) {
        const pluginSchema = response.schemas.plugin_json_schema as { default?: FormData };
        setPluginFormData(pluginSchema.default || {});
      }
      
    } catch (err: unknown) {
      const message = err instanceof Error ? err.message : 'Failed to load schemas';
      setError(message);
      
      // Check if error is related to plugin not running
      if (message.includes('not running') || message.includes('Connection refused') || message.includes('404')) {
        toast.error(`Plugin ${pluginName} must be running to load configuration schemas`);
      } else {
        toast.error(`Failed to load configuration schemas: ${message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const loadInstances = async () => {
    setInstancesLoading(true);
    try {
      const response = await apiClient.listPluginInstances(pluginName);
      setInstances(response);
    } catch (err: unknown) {
            // Don't show error toast for instances loading failure

      const message = err instanceof Error ? err.message : 'Failed to load instances';
      console.error('Failed to load instances:', message);
    } finally {
      setInstancesLoading(false);
    }
  };

  const handleCreateInstance = async (data: IChangeEvent<FormData>) => {
    setSaving(true);
    try {
      // Clear previous errors
      setError(null);
      
      if (!data.formData) {
        throw new Error('No configuration data provided');
      }

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const response = await apiClient.createPluginInstance(pluginName, data.formData);
      
      toast.success(`Instance created successfully: ${response.instance_id.substring(0, 8)}...`);
      
      // Reload instances list
      await loadInstances();
      
      // Reset form to default values
      if (schemas?.schemas.plugin_json_schema) {
        const pluginSchema = schemas.schemas.plugin_json_schema as { default?: FormData };
        setPluginFormData(pluginSchema.default || {});
      }
      
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to create instance';
      toast.error(`Failed to create instance: ${message}`);
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteInstance = async (instanceId: string) => {
    try {
      await apiClient.deletePluginInstance(pluginName, instanceId);
      toast.success(`Instance ${instanceId.substring(0, 8)}... deleted successfully`);
      
      // Reload instances list
      await loadInstances();
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to delete instance';
      toast.error(`Failed to delete instance: ${message}`);
    }
  };

  const handleCopyInstanceId = (instanceId: string) => {
    navigator.clipboard.writeText(instanceId);
    toast.success('Instance ID copied to clipboard');
  };

  const handleFormError = (errors: RJSFValidationError[]) => {
    // Only show toast notification for validation errors, don't update state
    if (errors.length > 0) {
      const errorMessages = errors
        .filter(error => error.message)
        .map(error => error.message)
        .join(', ');
      
      toast.error(`Please fill in required fields: ${errorMessages}`);
    }
  };

  const renderInstanceCard = (instanceId: string, instance: PluginInstanceInfo) => {
    const shortId = instanceId.substring(0, 8);
    const isReady = instance.ready;

    return (
      <Card key={instanceId} className="relative">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Database size={16} className="text-muted-foreground" />
              <CardTitle className="text-sm font-medium">Instance {shortId}...</CardTitle>
              <Badge 
                variant={isReady ? "default" : "secondary"} 
                className={`text-xs flex items-center gap-1 ${
                  isReady 
                    ? 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800'
                    : 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800'
                }`}
              >
                {isReady ? (
                  <>
                    <CheckCircle size={10} />
                    Ready
                  </>
                ) : (
                  <>
                    <Clock size={10} />
                    Initializing
                  </>
                )}
              </Badge>
            </div>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleCopyInstanceId(instanceId)}
                className="h-8 w-8 p-0"
                title="Copy full instance ID"
              >
                <Copy size={14} />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleDeleteInstance(instanceId)}
                className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                title="Delete instance"
              >
                <Trash2 size={14} />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="text-xs text-muted-foreground space-y-1">
            <div>ID: <code className="bg-muted px-1 py-0.5 rounded text-xs">{instanceId}</code></div>
            <div className="flex items-center gap-2">
              <span>Status:</span>
              <div className={`flex items-center gap-1 ${isReady ? 'text-green-600 dark:text-green-400' : 'text-orange-600 dark:text-orange-400'}`}>
                {isReady ? (
                  <>
                    <CheckCircle size={12} />
                    Ready for use
                  </>
                ) : (
                  <>
                    <Clock size={12} />
                    Initializing...
                  </>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderCreateInstanceForm = () => {
    if (!schemas?.schemas.plugin_json_schema) {
      return (
        <div className="p-6">
          <div className="bg-amber-50 dark:bg-amber-950/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0">
                <AlertCircle className="h-5 w-5 text-amber-600 dark:text-amber-400" />
              </div>
              <div className="flex-1">
                <h3 className="text-sm font-semibold text-amber-800 dark:text-amber-200 mb-1">
                  No Configuration Schema Available
                </h3>
                <p className="text-sm text-amber-700 dark:text-amber-300">
                  No plugin configuration schema is available for this plugin. The plugin may not support configuration options.
                </p>
              </div>
            </div>
          </div>
        </div>
      );
    }

    // Generate the shadcn form components
    const ShadcnForm = generateForm();

    return (
      <div className="h-full flex flex-col">
        <div className="flex-1 overflow-y-auto px-6 pt-2 pb-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Plus size={18} />
                Create New Instance
              </CardTitle>
              <CardDescription>
                Configure a new instance for {pluginName} plugin
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ShadcnForm
                schema={schemas.schemas.plugin_json_schema as RJSFSchema}
                uiSchema={(schemas.schemas.plugin_ui_schema || {}) as UiSchema}
                formData={pluginFormData}
                validator={validator}
                onSubmit={handleCreateInstance}
                onError={handleFormError}
                onChange={(e: IChangeEvent<FormData>) => setPluginFormData(e.formData || {})}
                showErrorList={false}
                noHtml5Validate={false}
                liveValidate={false}
                id="plugin-config-form"
              >
                <div className="hidden">
                  {/* Hide the default submit button */}
                </div>
              </ShadcnForm>
            </CardContent>
          </Card>
        </div>
        
        <div className="flex-shrink-0 border-t bg-background px-6 py-4">
          <div className="flex gap-2">
            <Button 
              onClick={() => {
                // Manually trigger form submission
                const formElement = document.getElementById('plugin-config-form') as HTMLFormElement;
                if (formElement) {
                  formElement.requestSubmit();
                }
              }}
              disabled={saving}
              className="flex items-center gap-2"
            >
              {saving ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Plus size={16} />
                  Create Instance
                </>
              )}
            </Button>
            <Button 
              variant="outline" 
              disabled={saving}
              onClick={() => {
                const defaultData = (schemas.schemas.plugin_json_schema as { default?: FormData }).default || {};
                setPluginFormData(defaultData);
                toast.info('Reset to default configuration');
              }}
            >
              Reset to Default
            </Button>
          </div>
        </div>
      </div>
    );
  };

  const renderInstancesList = () => {
    if (instancesLoading) {
      return (
        <div className="h-full flex items-center justify-center">
          <div className="flex items-center">
            <Loader2 className="animate-spin" size={24} />
            <span className="ml-2">Loading instances...</span>
          </div>
        </div>
      );
    }

    const instanceEntries = Object.entries(instances?.instances.instances || {});

    return (
      <div className="h-full flex flex-col">
        <div className="flex-shrink-0 flex items-center justify-between px-6 pt-2 pb-4">
          <div>
            <h3 className="text-lg font-semibold">Plugin Instances</h3>
            <p className="text-sm text-muted-foreground">
              {instances?.instances.total_instances} instance{instances?.instances.total_instances !== 1 ? 's' : ''} found
            </p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={loadInstances}
            disabled={instancesLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw size={14} className={instancesLoading ? "animate-spin" : ""} />
            Refresh
          </Button>
        </div>

        <div className="flex-1 overflow-y-auto px-6 pb-6">
          <div className="grid gap-4">
            {instanceEntries.map(([instanceId, instance]) =>
              renderInstanceCard(instanceId, instance)
            )}
          </div>
        </div>
      </div>
    );
  };

  const hasPluginSchema = schemas?.schemas.plugin_json_schema && 
                          Object.keys(schemas.schemas.plugin_json_schema).length > 0;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="!max-w-[95vw] !w-[1400px] !h-[90vh] flex flex-col p-0 gap-0 sm:!max-w-[95vw]">
        <DialogHeader className="flex-shrink-0 px-6 pt-6 pb-4">
          <DialogTitle className="flex items-center gap-2">
            <Settings size={20} />
            Manage {pluginName} Instances
          </DialogTitle>
          <DialogDescription>
            Create and manage plugin instances with different configurations
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 min-h-0 flex flex-col">
          {loading && (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="animate-spin" size={24} />
              <span className="ml-2">Loading configuration schemas...</span>
            </div>
          )}

          {error && (
            <div className="mx-6 mb-6">
              <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0">
                    <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-sm font-semibold text-red-800 dark:text-red-200 mb-1">
                      {(error.includes('not running') || error.includes('Connection refused') || error.includes('404')) 
                        ? 'Plugin Not Running' 
                        : 'Configuration Error'}
                    </h3>
                    <p className="text-sm text-red-700 dark:text-red-300 mb-3">
                      {(error.includes('not running') || error.includes('Connection refused') || error.includes('404'))
                        ? `Plugin ${pluginName} must be running to access configuration management. Please start the plugin first.`
                        : error}
                    </p>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="border-red-300 dark:border-red-700 text-red-700 dark:text-red-300 hover:bg-red-50 dark:hover:bg-red-950/30"
                      onClick={loadSchemas}
                    >
                      <RefreshCw size={14} className="mr-1" />
                      Retry Loading
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {schemas && !loading && (
            <Tabs defaultValue="instances" className="flex-1 min-h-0 flex flex-col">
              <TabsList className="mx-6 mb-4 grid w-fit grid-cols-2 flex-shrink-0">
                <TabsTrigger value="instances" className="flex items-center gap-2">
                  <Eye size={16} />
                  View Instances
                </TabsTrigger>
                <TabsTrigger value="create" className="flex items-center gap-2" disabled={!hasPluginSchema}>
                  <Plus size={16} />
                  Create Instance
                </TabsTrigger>
              </TabsList>
              
              <div className="flex-1 min-h-0 overflow-hidden">
                <TabsContent value="instances" className="h-full overflow-y-auto mt-0">
                  {renderInstancesList()}
                </TabsContent>
                
                <TabsContent value="create" className="h-full overflow-y-auto mt-0">
                  {renderCreateInstanceForm()}
                </TabsContent>
              </div>
            </Tabs>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
} 