"use client"

import { useTheme } from "next-themes"
import { Toaster as Son<PERSON>, ToasterProps } from "sonner"

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme()

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      toastOptions={{
        classNames: {
          error: "!bg-red-600 !border-red-700 !text-white [&>div]:!text-white",
          success: "!bg-green-600 !border-green-700 !text-white [&>div]:!text-white",
          warning: "!bg-yellow-600 !border-yellow-700 !text-white [&>div]:!text-white",
          info: "!bg-blue-600 !border-blue-700 !text-white [&>div]:!text-white",
        },
      }}
      style={
        {
          "--normal-bg": "var(--popover)",
          "--normal-text": "var(--popover-foreground)",
          "--normal-border": "var(--border)",
        } as React.CSSProperties
      }
      {...props}
    />
  )
}

export { Toaster }
