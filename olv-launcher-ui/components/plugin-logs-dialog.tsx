/* eslint-disable react-hooks/exhaustive-deps */
import { useState, useEffect, useRef } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Loader2, Terminal, Trash2, Download, RefreshCw, AlertCircle } from 'lucide-react';
import { apiClient } from '@/lib/api';
import { usePluginActions } from '@/hooks/use-plugins';
import { toast } from 'sonner';

interface PluginLogsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  pluginName: string;
}

export function PluginLogsDialog({ open, onOpenChange, pluginName }: PluginLogsDialogProps) {
  const [logs, setLogs] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [clearing, setClearing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const { clearLogs } = usePluginActions();
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (open && pluginName) {
      loadLogs();
    } else {
      // Reset state when dialog closes
      setError(null);
      setAutoRefresh(false);
    }
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [open, pluginName]);

  useEffect(() => {
    if (autoRefresh && open) {
      intervalRef.current = setInterval(() => loadLogs(true), 3000); // Refresh every 3 seconds
    } else if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [autoRefresh, open]);

  // Auto-scroll to bottom when new logs are added
  useEffect(() => {
    if (scrollAreaRef.current && logs.length > 0) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [logs]);

  const loadLogs = async (isAutoRefresh = false) => {
    if (!pluginName) return;
    
    if (!isAutoRefresh) {
      setLoading(true);
    } else {
      setRefreshing(true);
    }
    setError(null);
    
    try {
      const response = await apiClient.getPluginLogs(pluginName, 200);
      setLogs(response.logs);
      
      if (!isAutoRefresh) {
        // toast.success('Logs loaded successfully');
      }
    } catch (err: unknown) {
      const message = err instanceof Error ? err.message : 'Failed to load logs';
      setError(message);
      if (!isAutoRefresh) {
        toast.error(`Failed to load logs: ${message}`);
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleClearLogs = async () => {
    setClearing(true);
    try {
      await clearLogs(pluginName);
      setLogs([]);
      // toast.success('Logs cleared successfully');
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to clear logs';
      toast.error(`Failed to clear logs: ${message}`);
    } finally {
      setClearing(false);
    }
  };

  const handleDownloadLogs = () => {
    try {
      const logContent = logs.join('\n');
      const blob = new Blob([logContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${pluginName}-logs-${new Date().toISOString().split('T')[0]}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      // toast.success('Logs downloaded successfully');
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to download logs';
      toast.error(message);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="!max-w-[95vw] !w-[1400px] max-h-[90vh] overflow-hidden flex flex-col sm:!max-w-[95vw] bg-slate-950 border-slate-800">
        <DialogHeader className="flex-shrink-0 border-b border-slate-800 pb-4">
          <DialogTitle className="flex items-center gap-2 text-green-400 font-mono">
            <Terminal size={20} />
            terminal@olv:~$ tail -f {pluginName}.log
          </DialogTitle>
          <DialogDescription className="text-slate-400 font-mono">
            Real-time log streaming for {pluginName} plugin
          </DialogDescription>
        </DialogHeader>

        {/* Terminal Header Bar */}
        <div className="flex items-center justify-between bg-slate-900/50 rounded-t-lg px-4 py-2 border-b border-slate-700">
          <div className="flex items-center gap-2">
            <div className="flex gap-2">
              <div className="w-3 h-3 rounded-full bg-red-500"></div>
              <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
              <div className="w-3 h-3 rounded-full bg-green-500"></div>
            </div>
            <span className="text-slate-400 font-mono text-sm ml-4">
              {pluginName} - Log Terminal
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => loadLogs()}
              disabled={loading || refreshing}
              className="h-7 px-2 text-slate-300 hover:text-green-400 hover:bg-slate-800 font-mono text-xs"
            >
              <RefreshCw size={12} className={loading || refreshing ? 'animate-spin' : ''} />
              {refreshing ? 'syncing...' : 'refresh'}
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setAutoRefresh(!autoRefresh)}
              className={`h-7 px-2 font-mono text-xs ${
                autoRefresh 
                  ? 'text-green-400 bg-green-400/10 hover:bg-green-400/20' 
                  : 'text-slate-300 hover:text-green-400 hover:bg-slate-800'
              }`}
            >
              <RefreshCw size={12} className={autoRefresh ? 'animate-spin' : ''} />
              auto
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={handleDownloadLogs}
              disabled={logs.length === 0}
              className="h-7 px-2 text-slate-300 hover:text-blue-400 hover:bg-slate-800 font-mono text-xs"
            >
              <Download size={12} />
              save
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={handleClearLogs}
              disabled={logs.length === 0 || clearing}
              className="h-7 px-2 text-slate-300 hover:text-red-400 hover:bg-slate-800 font-mono text-xs"
            >
              {clearing ? (
                <Loader2 size={12} className="animate-spin" />
              ) : (
                <Trash2 size={12} />
              )}
              clear
            </Button>
          </div>
        </div>

        {/* Terminal Content */}
        <div className="flex-1 bg-black/90 relative rounded-b-lg flex flex-col min-h-0">
          {loading && logs.length === 0 && (
            <div className="flex items-center justify-center py-8 text-green-400">
              <Loader2 className="animate-spin" size={24} />
              <span className="ml-2 font-mono">Initializing terminal...</span>
            </div>
          )}

          {error && (
            <div className="p-4">
              <div className="bg-red-900/20 border border-red-700 rounded p-3">
                <div className="flex items-center gap-2 text-red-400 font-mono text-sm">
                  <AlertCircle className="h-4 w-4" />
                  <span>ERROR: {error}</span>
                </div>
                <Button 
                  variant="ghost"
                  size="sm" 
                  className="mt-2 text-red-400 hover:text-red-300 hover:bg-red-900/30 font-mono text-xs"
                  onClick={() => loadLogs()}
                >
                  retry connection
                </Button>
              </div>
            </div>
          )}

          {logs.length > 0 ? (
            <div 
              className="flex-1 overflow-y-auto overflow-x-hidden p-4 terminal-scroll"
              ref={scrollAreaRef}
            >
              <div className="space-y-0 font-mono text-sm leading-relaxed">
                {logs.map((line, index) => {
                  // Parse timestamp and log level
                  const timestampMatch = line.match(/^\[([\d-:\s]+)\]/);
                  const levelMatch = line.match(/\[(DEBUG|INFO|WARNING|ERROR|CRITICAL)\]/i);
                  
                  let lineColor = 'text-slate-300';
                  let bgClass = '';
                  
                  if (levelMatch) {
                    switch (levelMatch[1].toUpperCase()) {
                      case 'ERROR':
                      case 'CRITICAL':
                        lineColor = 'text-red-400';
                        bgClass = 'bg-red-900/10';
                        break;
                      case 'WARNING':
                        lineColor = 'text-yellow-400';
                        bgClass = 'bg-yellow-900/10';
                        break;
                      case 'INFO':
                        lineColor = 'text-blue-400';
                        bgClass = 'bg-blue-900/10';
                        break;
                      case 'DEBUG':
                        lineColor = 'text-slate-500';
                        break;
                    }
                  }

                  return (
                    <div 
                      key={index} 
                      className={`group px-2 py-0.5 rounded-sm hover:bg-slate-800/30 transition-colors ${bgClass}`}
                    >
                      <div className="flex items-start gap-2">
                        <span className="text-slate-600 text-xs leading-relaxed select-none min-w-[3ch] flex-shrink-0">
                          {String(index + 1).padStart(3, '0')}
                        </span>
                        <div className={`${lineColor} break-words whitespace-pre-wrap flex-1 leading-relaxed`}>
                          {timestampMatch && (
                            <span className="text-slate-500 mr-2">
                              {timestampMatch[1]}
                            </span>
                          )}
                          {levelMatch && (
                            <span className={`inline-block px-1.5 py-0.5 rounded text-xs font-bold mr-2 ${
                              levelMatch[1].toUpperCase() === 'ERROR' || levelMatch[1].toUpperCase() === 'CRITICAL'
                                ? 'bg-red-600 text-white'
                                : levelMatch[1].toUpperCase() === 'WARNING'
                                ? 'bg-yellow-600 text-black'
                                : levelMatch[1].toUpperCase() === 'INFO'
                                ? 'bg-blue-600 text-white'
                                : 'bg-slate-600 text-white'
                            }`}>
                              {levelMatch[1].toUpperCase()}
                            </span>
                          )}
                          <span>{line.replace(/^\[[\d-:\s]+\]/, '').replace(/\[(DEBUG|INFO|WARNING|ERROR|CRITICAL)\]/i, '').trim()}</span>
                        </div>
                      </div>
                    </div>
                  );
                })}
                
                {/* Terminal Cursor */}
                <div className="flex items-center gap-2 mt-2 pt-2 border-t border-slate-800">
                  <span className="text-green-400 text-xs">$</span>
                  <span className="text-slate-400 text-xs">
                    {autoRefresh ? 'auto-refreshing...' : 'ready'}
                  </span>
                  <div className="w-2 h-4 bg-green-400 animate-pulse ml-1"></div>
                </div>
              </div>
            </div>
          ) : !loading && !error && (
            <div className="flex flex-col items-center justify-center py-12 text-slate-500">
              <Terminal size={48} className="mb-4 opacity-50" />
              <div className="text-center font-mono">
                <div className="text-lg mb-2">No log entries found</div>
                <div className="text-sm">Terminal is ready for input...</div>
              </div>
            </div>
          )}
        </div>

        {/* Terminal Status Bar */}
        <div className="flex items-center justify-between bg-slate-900 px-4 py-2 text-xs font-mono border-t border-slate-700 rounded-b-lg">
          <div className="flex items-center gap-4">
            <span className="text-slate-400">
              {logs.length > 0 ? `${logs.length} lines` : 'empty'}
            </span>
            {autoRefresh && (
              <span className="flex items-center gap-1 text-green-400">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                live
              </span>
            )}
          </div>
          <div className="flex items-center gap-4 text-slate-500">
            <span>encoding: utf-8</span>
            <span>tail -f mode</span>
            <span>{new Date().toLocaleTimeString()}</span>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 