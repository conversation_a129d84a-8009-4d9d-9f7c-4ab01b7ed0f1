/* eslint-disable @typescript-eslint/no-unused-vars */
'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Save,
  RotateCcw,
  Trash2,
  Plus,
  Folder,
  Clock,
  AlertCircle,
  CheckCircle,
  Loader2,
  <PERSON>ting<PERSON>,
  Star,
  <PERSON>Off
} from 'lucide-react';
import { useWorkspaces, useWorkspaceActions, useWorkspaceSettings } from '@/hooks/use-workspaces';
import { mutate } from 'swr';
import { toast } from 'sonner';

interface WorkspaceManagerProps {
  onRefresh?: () => void;
}

export function WorkspaceManager({ onRefresh }: WorkspaceManagerProps) {
  const { data: workspacesData, error, isLoading, mutate: mutateWorkspaces } = useWorkspaces();
  const { data: settingsData, mutate: mutateSettings } = useWorkspaceSettings();
  const { saveWorkspace, restoreWorkspace, deleteWorkspace, updateSettings, loading } = useWorkspaceActions();

  const [saveDialogOpen, setSaveDialogOpen] = useState(false);
  const [workspaceName, setWorkspaceName] = useState('');
  const [workspaceDescription, setWorkspaceDescription] = useState('');
  const [saveMode, setSaveMode] = useState<'new' | 'current'>('new');

  const workspaces = workspacesData?.workspaces || [];
  const currentWorkspace = workspacesData?.current_workspace;
  const defaultWorkspace = settingsData?.default_workspace;
  const autoUpdateEnabled = settingsData?.auto_update_enabled || false;

  const handleSaveWorkspace = async () => {
    // Validate input based on save mode
    if (saveMode === 'new' && !workspaceName.trim()) {
      toast.error('Please enter a workspace name');
      return;
    }

    if (saveMode === 'current' && !currentWorkspace) {
      toast.error('No current workspace to save to');
      return;
    }

    try {
      await saveWorkspace({
        name: saveMode === 'new' ? workspaceName.trim() : undefined,
        description: workspaceDescription.trim() || undefined,
        save_to_current: saveMode === 'current',
      });

      // Refresh workspaces list
      await mutateWorkspaces();

      // Reset form
      setWorkspaceName('');
      setWorkspaceDescription('');
      setSaveMode('new');
      setSaveDialogOpen(false);

      // Refresh main page if callback provided
      onRefresh?.();
    } catch (error) {
      // Error is handled in the hook
    }
  };

  const handleRestoreWorkspace = async (name: string) => {
    try {
      await restoreWorkspace(name);

      // Refresh workspaces list and main page
      await mutateWorkspaces();
      await mutate('plugins'); // Refresh plugins list
      onRefresh?.();
    } catch (error) {
      // Error is handled in the hook
    }
  };

  const handleDeleteWorkspace = async (name: string) => {
    if (!confirm(`Are you sure you want to delete workspace "${name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      await deleteWorkspace(name);

      // Refresh workspaces list
      await mutateWorkspaces();
    } catch (error) {
      // Error is handled in the hook
    }
  };

  const handleSetDefaultWorkspace = async (workspaceName: string) => {
    try {
      await updateSettings({ default_workspace: workspaceName });
      await mutateSettings();
      toast.success(`Set "${workspaceName}" as default workspace`);
    } catch (error) {
      // Error is handled in the hook
    }
  };

  const handleToggleAutoUpdate = async () => {
    try {
      await updateSettings({ auto_update_enabled: !autoUpdateEnabled });
      await mutateSettings();
    } catch (error) {
      // Error is handled in the hook
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load workspaces: {error.message}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <CardTitle className="flex items-center gap-2">
              <Folder className="h-5 w-5" />
              Workspace Manager
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              Save and restore plugin configurations and running states
            </p>
          </div>

          <Dialog open={saveDialogOpen} onOpenChange={setSaveDialogOpen}>
            <DialogTrigger asChild>
              <Button
                variant="default"
                size="sm"
                disabled={loading.save}
                className="flex items-center gap-2"
              >
                {loading.save ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Save className="h-4 w-4" />
                )}
                Save Current State
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Save Workspace</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="save-mode">Save Mode</Label>
                  <Select value={saveMode} onValueChange={(value: 'new' | 'current') => setSaveMode(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="new">Create New Workspace</SelectItem>
                      <SelectItem value="current" disabled={!currentWorkspace}>
                        Save to Current Workspace {currentWorkspace ? `(${currentWorkspace})` : '(None)'}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {saveMode === 'new' && (
                  <div className="space-y-2">
                    <Label htmlFor="workspace-name">Workspace Name</Label>
                    <Input
                      id="workspace-name"
                      placeholder="Enter workspace name..."
                      value={workspaceName}
                      onChange={(e) => setWorkspaceName(e.target.value)}
                    />
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="workspace-description">Description (Optional)</Label>
                  <Textarea
                    id="workspace-description"
                    placeholder="Enter workspace description..."
                    value={workspaceDescription}
                    onChange={(e) => setWorkspaceDescription(e.target.value)}
                    rows={3}
                  />
                </div>

                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSaveDialogOpen(false);
                      setSaveMode('new');
                      setWorkspaceName('');
                      setWorkspaceDescription('');
                    }}
                    disabled={loading.save}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSaveWorkspace}
                    disabled={loading.save || (saveMode === 'new' && !workspaceName.trim()) || (saveMode === 'current' && !currentWorkspace)}
                  >
                    {loading.save ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        {saveMode === 'new' ? 'Create Workspace' : 'Save to Current'}
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>

      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>Loading workspaces...</span>
          </div>
        ) : workspaces.length === 0 ? (
          <div className="text-center py-8">
            <Folder className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No Workspaces Saved</h3>
            <p className="text-muted-foreground mb-4">
              Save your current plugin configuration to create your first workspace.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              {currentWorkspace && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>Current: <strong>{currentWorkspace}</strong></span>
                  {defaultWorkspace === currentWorkspace && (
                    <div title="Default workspace">
                      <Star className="h-3 w-3 text-yellow-500" />
                    </div>
                  )}
                </div>
              )}

              <Button
                variant={autoUpdateEnabled ? "default" : "outline"}
                size="sm"
                onClick={handleToggleAutoUpdate}
                disabled={loading.settings}
                className="text-xs h-6 px-2"
                title={autoUpdateEnabled ? "Auto-update enabled" : "Auto-update disabled"}
              >
                {loading.settings ? (
                  <Loader2 className="h-3 w-3 animate-spin" />
                ) : (
                  <>
                    <Settings className="h-3 w-3 mr-1" />
                    Auto
                  </>
                )}
              </Button>
            </div>

            <Separator />

            <div className="space-y-3">
              {workspaces.map((workspace) => (
                <div
                  key={workspace.name}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium truncate">{workspace.name}</h4>
                      {workspace.name === currentWorkspace && (
                        <Badge variant="secondary" className="text-xs">Current</Badge>
                      )}
                    </div>
                    {workspace.description && (
                      <p className="text-sm text-muted-foreground mb-2 truncate">
                        {workspace.description}
                      </p>
                    )}
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {formatDate(workspace.created_at)}
                      </span>
                      <span>{workspace.plugins.length} plugins</span>
                      <span>
                        {workspace.plugins.reduce((total, plugin) => total + plugin.instances.length, 0)} instances
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSetDefaultWorkspace(workspace.name)}
                      disabled={loading.settings || defaultWorkspace === workspace.name}
                      className="flex items-center gap-1"
                      title={defaultWorkspace === workspace.name ? "Already default" : "Set as default"}
                    >
                      {defaultWorkspace === workspace.name ? (
                        <Star className="h-3 w-3 text-yellow-500" />
                      ) : (
                        <StarOff className="h-3 w-3" />
                      )}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRestoreWorkspace(workspace.name)}
                      disabled={loading[workspace.name]}
                      className="flex items-center gap-1"
                    >
                      {loading[workspace.name] ? (
                        <Loader2 className="h-3 w-3 animate-spin" />
                      ) : (
                        <RotateCcw className="h-3 w-3" />
                      )}
                      Restore
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteWorkspace(workspace.name)}
                      disabled={loading[`delete_${workspace.name}`]}
                      className="flex items-center gap-1 text-destructive hover:text-destructive"
                    >
                      {loading[`delete_${workspace.name}`] ? (
                        <Loader2 className="h-3 w-3 animate-spin" />
                      ) : (
                        <Trash2 className="h-3 w-3" />
                      )}
                      Delete
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
