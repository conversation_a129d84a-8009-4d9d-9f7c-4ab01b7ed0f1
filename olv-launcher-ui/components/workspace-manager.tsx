'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import {
  Save,
  RotateCcw,
  Trash2,
  Plus,
  Folder,
  Clock,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { useWorkspaces, useWorkspaceActions } from '@/hooks/use-workspaces';
import { mutate } from 'swr';
import { toast } from 'sonner';

interface WorkspaceManagerProps {
  onRefresh?: () => void;
}

export function WorkspaceManager({ onRefresh }: WorkspaceManagerProps) {
  const { data: workspacesData, error, isLoading, mutate: mutateWorkspaces } = useWorkspaces();
  const { saveWorkspace, restoreWorkspace, deleteWorkspace, loading } = useWorkspaceActions();

  const [saveDialogOpen, setSaveDialogOpen] = useState(false);
  const [workspaceName, setWorkspaceName] = useState('');
  const [workspaceDescription, setWorkspaceDescription] = useState('');

  const workspaces = workspacesData?.workspaces || [];
  const currentWorkspace = workspacesData?.current_workspace;

  const handleSaveWorkspace = async () => {
    if (!workspaceName.trim()) {
      toast.error('Please enter a workspace name');
      return;
    }

    try {
      await saveWorkspace({
        name: workspaceName.trim(),
        description: workspaceDescription.trim() || undefined,
      });

      // Refresh workspaces list
      await mutateWorkspaces();

      // Reset form
      setWorkspaceName('');
      setWorkspaceDescription('');
      setSaveDialogOpen(false);

      // Refresh main page if callback provided
      onRefresh?.();
    } catch (error) {
      // Error is handled in the hook
    }
  };

  const handleRestoreWorkspace = async (name: string) => {
    try {
      await restoreWorkspace(name);

      // Refresh workspaces list and main page
      await mutateWorkspaces();
      await mutate('plugins'); // Refresh plugins list
      onRefresh?.();
    } catch (error) {
      // Error is handled in the hook
    }
  };

  const handleDeleteWorkspace = async (name: string) => {
    if (!confirm(`Are you sure you want to delete workspace "${name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      await deleteWorkspace(name);

      // Refresh workspaces list
      await mutateWorkspaces();
    } catch (error) {
      // Error is handled in the hook
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load workspaces: {error.message}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              <Folder className="h-4 w-4" />
              Workspaces
            </CardTitle>

            <Dialog open={saveDialogOpen} onOpenChange={setSaveDialogOpen}>
              <DialogTrigger asChild>
                <Button
                  variant="default"
                  size="sm"
                  disabled={loading.save}
                  className="flex items-center gap-1"
                >
                  {loading.save ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    <Save className="h-3 w-3" />
                  )}
                  Save State
                </Button>
              </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Save Current Workspace</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="workspace-name">Workspace Name</Label>
                  <Input
                    id="workspace-name"
                    placeholder="Enter workspace name..."
                    value={workspaceName}
                    onChange={(e) => setWorkspaceName(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="workspace-description">Description (Optional)</Label>
                  <Textarea
                    id="workspace-description"
                    placeholder="Enter workspace description..."
                    value={workspaceDescription}
                    onChange={(e) => setWorkspaceDescription(e.target.value)}
                    rows={3}
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setSaveDialogOpen(false)}
                    disabled={loading.save}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSaveWorkspace}
                    disabled={loading.save || !workspaceName.trim()}
                  >
                    {loading.save ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Save Workspace
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          {currentWorkspace && (
            <div className="flex items-center gap-2 text-xs text-muted-foreground bg-green-50 dark:bg-green-950 px-2 py-1 rounded">
              <CheckCircle className="h-3 w-3 text-green-600" />
              <span>Current: <strong>{currentWorkspace}</strong></span>
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {isLoading ? (
          <div className="flex items-center justify-center py-4">
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
            <span className="text-sm">Loading...</span>
          </div>
        ) : workspaces.length === 0 ? (
          <div className="text-center py-4">
            <Folder className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">
              No workspaces saved yet
            </p>
          </div>
        ) : (
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {workspaces.map((workspace) => (
              <div
                key={workspace.name}
                className="flex items-center justify-between p-2 border rounded text-sm"
              >
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium truncate">{workspace.name}</span>
                    {workspace.name === currentWorkspace && (
                      <Badge variant="secondary" className="text-xs px-1 py-0">Current</Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-3 text-xs text-muted-foreground">
                    <span>{workspace.plugins.length} plugins</span>
                    <span>
                      {workspace.plugins.reduce((total, plugin) => total + plugin.instances.length, 0)} instances
                    </span>
                  </div>
                </div>

                <div className="flex items-center gap-1 ml-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRestoreWorkspace(workspace.name)}
                    disabled={loading[workspace.name]}
                    className="h-7 px-2"
                  >
                    {loading[workspace.name] ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      <RotateCcw className="h-3 w-3" />
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteWorkspace(workspace.name)}
                    disabled={loading[`delete_${workspace.name}`]}
                    className="h-7 px-2 text-destructive hover:text-destructive"
                  >
                    {loading[`delete_${workspace.name}`] ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      <Trash2 className="h-3 w-3" />
                    )}
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
