/* eslint-disable @typescript-eslint/no-unused-vars */
'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { PluginCard } from '@/components/plugin-card';
import { PluginStatusBadge } from '@/components/plugin-status-badge';
import { WorkspaceManager } from '@/components/workspace-manager';
import { usePlugins, usePluginsByService } from '@/hooks/use-plugins';
import { mutate } from 'swr';
import { toast } from 'sonner';
import {
  Server,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  Monitor,
  Cloud,
} from 'lucide-react';
import { PluginStatus } from '@/lib/api';

export default function HomePage() {
  const { data: pluginsData, error: pluginsError, isLoading: pluginsLoading } = usePlugins();
  const [activeServiceTab, setActiveServiceTab] = useState<string>('all');
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await Promise.all([
        mutate('health'),
        mutate('plugins'),
        activeServiceTab !== 'all' ? mutate(`plugins/${activeServiceTab}`) : Promise.resolve()
      ]);
      // toast.success('Data refreshed successfully');
    } catch (error) {
      toast.error('Failed to refresh data');
    } finally {
      setIsRefreshing(false);
    }
  };

  const serviceTypes = ['asr', 'tts', 'llm'];
  const plugins = pluginsData?.plugins || [];

  const getPluginsByService = (serviceType: string) => {
    if (serviceType === 'all') return plugins;
    return plugins.filter(plugin => plugin.service_type.toLowerCase() === serviceType.toLowerCase());
  };

  const getServiceStats = () => {
    const stats = {
      total: plugins.length,
      running: plugins.filter(p => p.status === PluginStatus.RUNNING).length,
      stopped: plugins.filter(p => p.status === PluginStatus.STOPPED).length,
      error: plugins.filter(p => [PluginStatus.ERROR].includes(p.status)).length,
      local: plugins.filter(p => p.is_local).length,
      remote: plugins.filter(p => !p.is_local).length,
    };
    return stats;
  };

  const stats = getServiceStats();

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-card/50 backdrop-blur supports-[backdrop-filter]:bg-card/60">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <h1 className="text-3xl font-bold tracking-tight">OLV Launcher</h1>
              <p className="text-muted-foreground">
                Central plugin and service management for OLV platform
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={ pluginsLoading || isRefreshing}
                className="flex items-center gap-2"
              >
                <RefreshCw size={16} className={pluginsLoading || isRefreshing ? 'animate-spin' : ''} />
                {isRefreshing ? 'Refreshing...' : 'Refresh'}
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8 space-y-8">
        {/* System Status */}
        <section className="space-y-4">
          <h2 className="text-2xl font-semibold tracking-tight">System Status</h2>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {/* Total Plugins */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Plugins</CardTitle>
                <Server className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.total}</div>
                <p className="text-xs text-muted-foreground">
                  Discovered plugins
                </p>
              </CardContent>
            </Card>

            {/* Running Services */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Running Services</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{stats.running}</div>
                <p className="text-xs text-muted-foreground">
                  Active services
                </p>
              </CardContent>
            </Card>

            {/* Local Plugins */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Local Plugins</CardTitle>
                <Monitor className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{stats.local}</div>
                <p className="text-xs text-muted-foreground">
                  Locally managed
                </p>
              </CardContent>
            </Card>

            {/* Remote Plugins */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Remote Plugins</CardTitle>
                <Cloud className="h-4 w-4 text-purple-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">{stats.remote}</div>
                <p className="text-xs text-muted-foreground">
                  Remotely hosted
                </p>
              </CardContent>
            </Card>


          </div>
        </section>

        {/* Workspace Management */}
        <section className="space-y-4">
          <WorkspaceManager onRefresh={handleRefresh} />
        </section>

        {/* Plugin Management */}
        <section className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-semibold tracking-tight">Plugin Management</h2>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Clock size={14} />
              <span>Auto-refresh enabled</span>
            </div>
          </div>

          {pluginsError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Failed to load plugin information: {pluginsError.message}
                <Button
                  variant="outline"
                  size="sm"
                  className="ml-2"
                  onClick={handleRefresh}
                  disabled={isRefreshing}
                >
                  Retry
                </Button>
              </AlertDescription>
            </Alert>
          )}

          <Tabs value={activeServiceTab} onValueChange={setActiveServiceTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="all" className="flex items-center gap-2">
                <Server size={16} />
                All ({stats.total})
              </TabsTrigger>
              {serviceTypes.map(serviceType => {
                const servicePlugins = getPluginsByService(serviceType);
                return (
                  <TabsTrigger
                    key={serviceType}
                    value={serviceType}
                    className="flex items-center gap-2"
                  >
                    {serviceType.toUpperCase()} ({servicePlugins.length})
                  </TabsTrigger>
                );
              })}
            </TabsList>

            <TabsContent value="all" className="mt-6">
              {pluginsLoading ? (
                <div className="flex items-center justify-center py-12">
                  <RefreshCw className="animate-spin" size={24} />
                  <span className="ml-2">Loading plugins...</span>
                </div>
              ) : (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {plugins.map((plugin) => (
                    <PluginCard
                      key={plugin.name}
                      plugin={plugin}
                      onRefresh={handleRefresh}
                    />
                  ))}
                </div>
              )}
            </TabsContent>

            {serviceTypes.map(serviceType => (
              <TabsContent key={serviceType} value={serviceType} className="mt-6">
                <ServiceTabContent
                  serviceType={serviceType}
                  onRefresh={handleRefresh}
                />
              </TabsContent>
            ))}
          </Tabs>

          {plugins.length === 0 && !pluginsLoading && (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Server size={48} className="text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No Plugins Found</h3>
                <p className="text-muted-foreground text-center max-w-md">
                  No plugins were discovered in the plugins directory.
                  Make sure your plugins are properly installed and configured.
                </p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={handleRefresh}
                  disabled={isRefreshing}
                >
                  <RefreshCw size={16} className={isRefreshing ? 'animate-spin' : ''} />
                  Refresh
                </Button>
              </CardContent>
            </Card>
          )}
        </section>
      </main>
    </div>
  );
}

// Service Tab Content Component
function ServiceTabContent({ serviceType, onRefresh }: { serviceType: string; onRefresh: () => void }) {
  const { data: serviceData, error, isLoading } = usePluginsByService(serviceType);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <RefreshCw className="animate-spin" size={24} />
        <span className="ml-2">Loading {serviceType.toUpperCase()} plugins...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load {serviceType.toUpperCase()} plugins: {error.message}
        </AlertDescription>
      </Alert>
    );
  }

  const plugins = serviceData?.plugins || [];

  if (plugins.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Server size={48} className="text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">No {serviceType.toUpperCase()} Plugins</h3>
          <p className="text-muted-foreground text-center max-w-md">
            No {serviceType.toUpperCase()} plugins were found.
            Install {serviceType.toUpperCase()} plugins to manage them here.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {plugins.map((plugin) => (
        <PluginCard
          key={plugin.name}
          plugin={plugin}
          onRefresh={onRefresh}
        />
      ))}
    </div>
  );
}
