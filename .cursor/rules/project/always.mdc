---
description: 
globs: 
alwaysApply: true
---
This is a long-term project in the early, internal development stage and not yet online. 
Please use best practices and optimal architecture for long-term maintenance.
When modifying code and documentation, backward compatibility does not need to be considered, and the code version should always be 1.0.0. Do not include comments about deprecation or compatibility with old versions. Immediately remove any unused code instead of retaining it.